#pragma once
#include "nlohmann/json.hpp"
#include "macro_defs.h"

using json = nlohmann::json;

namespace dev_control_policy
{
    // 子设备结构体
    struct Device
    {
        bool is_enabled;

        // 比较操作符
        bool operator==(const Device& other) const
        {
            return is_enabled == other.is_enabled;
        }

        bool operator!=(const Device& other) const
        {
            return !(*this == other);
        }
    };

    // 顶层结构体
    struct Config
    {
        bool isOpen;
        Device storage_dev;
        Device cdrom;
        Device portable_dev;
        Device wifi_card;

        // 比较操作符
        bool operator==(const Config& other) const
        {
            return isOpen == other.isOpen &&
                   storage_dev == other.storage_dev &&
                   cdrom == other.cdrom &&
                   portable_dev == other.portable_dev &&
                   wifi_card == other.wifi_card;
        }

        bool operator!=(const Config& other) const
        {
            return !(*this == other);
        }
    };

    // 解析 Device
    void from_json(const json &j, Device &d)
    {
        j.at("is_enabled").get_to(d.is_enabled);
    }

    // 解析 Config
    void from_json(const json &j, Config &c)
    {
        j.at("isOpen").get_to(c.isOpen);
        j.at(STORAGE_DEV).get_to(c.storage_dev);
        j.at(CDROM_DEV).get_to(c.cdrom);
        j.at(PORTABLE_DEV).get_to(c.portable_dev);
        j.at(WIFI_CARD).get_to(c.wifi_card);
    }
};
