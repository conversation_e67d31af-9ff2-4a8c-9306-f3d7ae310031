#pragma once

#if defined(_USE_NXCOM)

//#define _STD_COMM

#ifdef _STD_COMM
#include <dlcom/cominc.h>
//using namespace StdCom;

#define ICommBase  StdCom::IBase
#define SComPtr    _lComPtr
#define NULLSComPtr  NULL
#define GetIID(x)  IID_ ## x 
#define ErrHR(x)   ((x >= E_NOTIMPL && x <= E_FAIL)?true:false)
#define RFAILED(x) { HRESULT _h_r_ = (x); if(ErrHR(_h_r_)) return _h_r_; }
#define NullPtr(x) x.m_p = NULL;
#define IsNullSComPtr(x) (x.m_p == NULL)

#else
#include <atcom/atcominc.h>
#include "atcom/atcombase.h"
#include "atcom/atplugin.h"

using namespace atsdk;
using namespace atcom;

#define ICommBase  IATBase
#define SComPtr    UTIL::com_ptr
#define GetIID(x)  __uuidof(x) 
#define NullPtr(x) x = INULL;
#define IsNullSComPtr(x) (x.m_p == NULL)
#define ErrHR(x)   FAILED(x)

#endif

#if defined(_USE_QLOG)
#include "qlogger.h"
#endif

template<class T>
class CommProxyBase
#ifdef _STD_COMM
	:public IPlugin, public IPluginRun, public CUnknownImp
#else
	:public IAtPlugin, public IAtPluginRun, public CUnknownImp
#endif
{
public:
	CommProxyBase()
	{

	}

	virtual ~CommProxyBase()
	{

	}

	virtual HRESULT OnAfterInit() { return S_OK; };
	virtual HRESULT OnBeforeUninit() { return S_OK; };
	virtual HRESULT OnAfterStart() { return S_OK; };
	virtual HRESULT OnBeforeStop() { return S_OK; };

	template<class t1>
	HRESULT QI(t1* p, REFIID riid, void** ppv)
	{
		return p->QueryInterface(riid, ppv);
	};

#ifdef _STD_COMM

	std_method_impl init_class(IBase* prot, IBase* punkOuter)
	{
		rc_assert(prot != NULL, E_FAIL)
		return prot->QueryInterface(IID_IComRunningObjectTable, (void**)&m_pRot);
	}
	std_method(Init)(int Argc, basic_tchar* Argv[], void* Object)
	{
		return OnAfterInit();
	}
	std_method(Uninit)()
	{
		auto hr = OnBeforeUninit();
#if defined(_USE_QLOG)
		QUnInitLog();
#endif
		return hr;
	}
	std_method(Start)(UINT uType)
	{
		return OnAfterStart();
	}
	std_method(Stop)(UINT uExitCode)
	{
		return OnBeforeStop();
	}

	HRESULT RotCreateInstance(REFCLSID clsid, REFIID iid, void** ppv)
	{
		return m_pRot->CreateInstance(m_pRot, clsid, nullptr, iid, ppv);
	}

	HRESULT RotGetObject(REFCLSID clsid, REFCLSID iid, void** ppunk)
	{
		return m_pRot->GetObject(clsid, iid, (IBase**)ppunk);
	}

protected:

	_lComPtr<IComRunningObjectTable>	m_pRot;

#else

	STDMETHOD(init_class)(IATBase* prot, IATBase* punkOuter)
	{
		return prot->QueryInterface(__uuidof(IAtcomRunningObjectTable), (void**)&m_pRot);
	}

	STDMETHOD(Init)(void*)
	{
		return OnAfterInit();
	}

	STDMETHOD(Uninit)()
	{
		auto hr = OnBeforeUninit();
#if defined(_USE_QLOG)
		QUnInitLog();
#endif
		return hr;
	}

	STDMETHOD(Start)()
	{
		return OnAfterStart();
	}

	STDMETHOD(Stop)()
	{
		return OnBeforeStop();
	}

	HRESULT RotCreateInstance(REFCLSID clsid, REFIID iid, void** ppv)
	{
		//IATBase* prot, const CLSID& rclsid, IATBase *punkOuter, const IID& riid, void **ppv

		UTIL::com_ptr<IObjectLoader> pObjectLoader;
		RFAILED(m_pRot->GetObject(CLSID_CObjectLoader, __uuidof(IObjectLoader), (IUnknown**)&pObjectLoader));
		RASSERT(pObjectLoader, E_FAIL);
		return pObjectLoader->CreateInstance(m_pRot, clsid, NULL, iid, (void**)ppv);
}

	HRESULT RotGetObject(const CLSID& rpid, const IID iid, void** ppunk)
	{
		return m_pRot->GetObject(rpid, iid, (IATBase**)ppunk);
	}

private:
	UTIL::com_ptr<IAtcomRunningObjectTable> m_pRot;
#endif

};



#ifdef _STD_COMM
#define  STD_QI()     \
					BEGIN_STDCOM_MAP\
						STDCOM_INTERFACE_ENTRY_UNKNOWN_(IPlugin)\
						STDCOM_INTERFACE_ENTRY(IPlugin)\
						STDCOM_INTERFACE_ENTRY(IPluginRun)\
					END_STDCOM_MAP
#define  STD_QI1(x1)\
					   BEGIN_STDCOM_MAP\
							STDCOM_INTERFACE_ENTRY_UNKNOWN_(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPluginRun)\
							STDCOM_INTERFACE_ENTRY(x1)\
					   END_STDCOM_MAP
 #define  STD_QI2(x1, x2)\
 					   BEGIN_STDCOM_MAP\
 							STDCOM_INTERFACE_ENTRY_UNKNOWN_(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPluginRun)\
 							STDCOM_INTERFACE_ENTRY(x1)\
 							STDCOM_INTERFACE_ENTRY(x2)\
 					   END_STDCOM_MAP
#define  STD_QI3(x1, x2, x3)\
 					   BEGIN_STDCOM_MAP\
 							STDCOM_INTERFACE_ENTRY_UNKNOWN_(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPluginRun)\
 							STDCOM_INTERFACE_ENTRY(x1)\
 							STDCOM_INTERFACE_ENTRY(x2)\
 							STDCOM_INTERFACE_ENTRY(x3)\
 					   END_STDCOM_MAP
#define  STD_QI4(x1, x2, x3, x4)\
 					   BEGIN_STDCOM_MAP\
 							STDCOM_INTERFACE_ENTRY_UNKNOWN_(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPluginRun)\
 							STDCOM_INTERFACE_ENTRY(x1)\
 							STDCOM_INTERFACE_ENTRY(x2)\
 							STDCOM_INTERFACE_ENTRY(x3)\
 							STDCOM_INTERFACE_ENTRY(x4)\
 						END_STDCOM_MAP
#define  STD_QI5(x1, x2, x3, x4, x5)\
 					   BEGIN_STDCOM_MAP\
 							STDCOM_INTERFACE_ENTRY_UNKNOWN_(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPluginRun)\
 							STDCOM_INTERFACE_ENTRY(x1)\
 							STDCOM_INTERFACE_ENTRY(x2)\
 							STDCOM_INTERFACE_ENTRY(x3)\
 							STDCOM_INTERFACE_ENTRY(x4)\
 							STDCOM_INTERFACE_ENTRY(x5)\
 						END_STDCOM_MAP
#define  STD_QI6(x1, x2, x3, x4, x5, x6)\
 					   BEGIN_STDCOM_MAP\
 							STDCOM_INTERFACE_ENTRY_UNKNOWN_(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPlugin)\
							STDCOM_INTERFACE_ENTRY(IPluginRun)\
 							STDCOM_INTERFACE_ENTRY(x1)\
 							STDCOM_INTERFACE_ENTRY(x2)\
 							STDCOM_INTERFACE_ENTRY(x3)\
 							STDCOM_INTERFACE_ENTRY(x4)\
 							STDCOM_INTERFACE_ENTRY(x5)\
 							STDCOM_INTERFACE_ENTRY(x6)\
 						END_STDCOM_MAP
#else
#define  STD_QI()     UNKNOWN_IMP2_(IAtPlugin, IAtPluginRun)
#define  STD_QI1(x1)\
					  UNKNOWN_IMP3_(IAtPlugin, IAtPluginRun, x1)
#define  STD_QI2(x1, x2)\
					  UNKNOWN_IMP4_(IAtPlugin, IAtPluginRun, x1, x2)
#define  STD_QI3(x1, x2, x3)\
					  UNKNOWN_IMP5_(IAtPlugin, IAtPluginRun, x1, x2, x3)
#define  STD_QI4(x1, x2, x3, x4)\
					  UNKNOWN_IMP6_(IAtPlugin, IAtPluginRun, x1, x2, x3, x4)
#define  STD_QI5(x1, x2, x3, x4, x5)\
					  UNKNOWN_IMP7_(IAtPlugin, IAtPluginRun, x1, x2, x3, x4, x5)
#define  STD_QI6(x1, x2, x3, x4, x5, x6)\
					  UNKNOWN_IMP8_(IAtPlugin, IAtPluginRun, x1, x2, x3, x4, x5, x6)
#endif

#ifdef _STD_COMM
#define _DEF_IID(iface, uuid_string, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) _DEFINE_IID(IID_ ## iface, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8)
#define _DEF_GUID(name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) _DEFINE_GUID_IMPL(name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8)
// 需要在interface.cpp里面 _DEFINE_IID_IMPL _DEFINE_GUID_IMPL
#else
#define _DEF_IID(iface, uuid_string, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8)	AT_DEFINE_IID(iface, uuid_string)
#define _DEF_GUID	AT_DEFINE_GUID
#endif

#ifdef _STD_COMM

#define BEGIN_CLID_MAP()		static const  STDCOM_OBJMAP_ENTRY ComClassObject[] =\
								{\
									{CLSID_STDCOM_ClassFactory,	&CoComFactoryCreator<CNullObjcetUnkown>::GetClassObject, "Plugin.Factory.impl.V1"},
#define CLID_MAP_ENTRY(CID, CLASS, PROGID)	{ CID, &CoComFactoryCreator<CLASS>::GetClassObject, PROGID },
#define END_CLID_MAP()						};\
									STD_COM_EXPORTS DllGetClassObject(REFCLSID rclsid, REFIID riid, void** ppv)\
									{\
										size_t i = 0;\
										for (i = 0; i < sizeof(ComClassObject) / sizeof(ComClassObject[0]); ++i){\
											if (rclsid == ComClassObject[i].clsid){\
												return ComClassObject[i].pfnGetClassObject(riid, ppv);\
											}\
										}\
										return E_INVALIDARG;\
									}\
									const STDCOM_OBJMAP_ENTRY* GetClassObjectMap(){	return ComClassObject;}\
									const long  GetClassObjectMapSize(){ return sizeof(ComClassObject) / sizeof(ComClassObject[0]); }\
									STD_METHOD(CLSID) DllGetAt(LONG nIndex) { const STDCOM_OBJMAP_ENTRY* pMap = GetClassObjectMap(); return pMap[nIndex + 1].clsid; }\
									STD_METHOD(LONG) DllGetCount() { const long lCount = GetClassObjectMapSize(); return (lCount > 0) ? lCount - 1 : 0; }\
									STD_METHOD(LPCSTR) DllProgIDFromCLSID(REFCLSID clsid) { \
											long i = 1; const STDCOM_OBJMAP_ENTRY* pMap = GetClassObjectMap(); \
											for (i = 1; i < GetClassObjectMapSize(); ++i) {\
													if (clsid == pMap[i].clsid)	{\
														return pMap[i].ProgID;\
													}\
											}\
											return "";\
									}\
									STD_COM_EXPORTS DllCanUnloadNow(void) { return S_OK; }\
									STD_COM_EXPORTS DllRegisterServer(void) { return S_OK; }\
									STD_COM_EXPORTS DllUnregisterServer(void) {  return S_OK; }\
									STD_COM_EXPORTS DllStartServer(void) { return S_OK; }\
									STD_COM_EXPORTS DllStopServer(void) { return S_OK; }
#else
#define BEGIN_CLID_MAP() BEGIN_CLIDMAP\
						 CLIDMAPENTRY_BEGIN
#define CLID_MAP_ENTRY(CID, CLASS, PROGID) CLIDMAPENTRY_PROGID(CID, CLASS, L ## PROGID)
#define END_CLID_MAP() CLIDMAPENTRY_END   END_CLIDMAP_AND_EXPORTFUN
#endif

#endif // _USE_NXCOM