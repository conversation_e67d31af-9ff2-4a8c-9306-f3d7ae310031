#include "usbprotector.h"
#include <thread>
#include <memory>
#include <vector>
#include "qlogger.h"
#if defined(_WIN32)
#include "devcon/cmds.h"
#else
    
#endif

using std::vector;
USBProtector::USBProtector(BlockingQueue<string>& report_msg_queue) : report_msg_queue_(report_msg_queue)
{
    run_flag_ = true;
}

USBProtector::~USBProtector()
{
}

void USBProtector::HandleAgentProxyMsg(const string &tag, const string &msg)
{
    if (0 == strcmp(tag.c_str(), "Plugin.Policy"))
    {
        try
            {
            json j = json::parse(msg);
            json jroot = json::parse(j[0].get<string>());
            if (jroot.at("type").get<string>() != "dev_control")
                return;

            QLogInfoUtf8(LOG_NAME, "policy: %s", msg.c_str());
            json jvalue = json::parse(jroot.at("value").get<string>());

            USBProtectorPolicy temp_policy = json::parse(jvalue.at("policy").get<string>());
            if (!NeedUpdateHostFirewallPolicy(temp_policy, curr_policy_))
            {
                QLogInfoUtf8(LOG_NAME, "HostFirewall::HandlePolicy The current policy is the same as the last time, "
                    "so the policy will not be updated.");
                return false;
            }
            
            curr_policy_ = temp_policy;
        }
        catch(const std::exception& e)
        {
            QLogErrorUtf8(LOG_NAME, "HandleAgentProxyMsg error: %s", e.what());
        }
        
    }
}

void USBProtector::Start()
{
    /*
        1、读取上一次策略，根据策略停止当前所有 u 盘设备或启用所有 u 盘设备。如果要精确管控，那么数据就要落盘。
        2、开启 u 盘监控，在监控中，如总开关为 false 则不工作，此外根据策略禁用插入的 u 盘，或者忽略。
        3、当有新策略到来，如果要启用所有 u 盘则开启，禁用则停止所有。
    */
#if defined(_WIN32)
    std::thread t(&USBManager::StartMonitor, &usb_manager_);
    t.detach();

    while (run_flag_)
    {
        auto usb_dev_id = usb_manager_.GetNotifyUSBDevId();
        if (!usb_dev_id.empty())
        {
            std::lock_guard<std::mutex> lg(curr_policy_mtx_);
            if (curr_policy_.isOpen)
            {
                if (curr_policy_.is_enable_)
                {
                    EnableUSBStorage(usb_dev_id);
                }
                else
                {
                    DisableUSBStorage(usb_dev_id);
                }
            }
        }
    }
#else
    std::thread t(&USBHotplugMonitor::StartMonitor, &usb_manager_);
    t.detach();
    
    while (run_flag_)
    {
        auto usb_dev = usb_manager_.GetNotifyUSBDevInfo();

        std::lock_guard<std::mutex> lg(curr_policy_mtx_);
        if (curr_policy_.is_open_)
        {
            if (curr_policy_.is_enable_)
            {
                EnableUSBStorage(usb_dev);
            }
            else
            {
                DisableUSBStorage(usb_dev);
            }
        }
    }
#endif
}

void USBProtector::Stop()
{
    run_flag_ = false;
#if defined(_WIN32)
    usb_manager_.StopMonitor();
#else
    usb_manager_.StopMonitor();
#endif
}

void USBProtector::EnableAllUSBStorage()
{
#if defined(_WIN32)
    vector<PTSTR> argv = {L"=DiskDrive", L"USBSTOR\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=WPD", L"USB\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=CDROM", L"USBSTOR\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=Net", L"USB\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());
#else
    usb_manager_.EnableTargetDevices();
#endif
}

void USBProtector::DisableAllUSBStorage()
{
#if defined(_WIN32)
    vector<PTSTR> argv = {L"=DiskDrive", L"USBSTOR\\*", nullptr}; // usb 存储设备
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=WPD", L"USB\\*", nullptr}; // 安卓手机 usb 数据连接
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=CDROM", L"USBSTOR\\*", nullptr}; 
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=Net", L"USB\\*", nullptr}; // usb 网卡
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());
#else
    usb_manager_.DisableTargetDevices();
#endif
}

template<typename T>
void USBProtector::EnableUSBStorage(const T &device)
{
// 忽略即可
#if defined(_WIN32)
#else
#endif
}

template<typename T>
void USBProtector::DisableUSBStorage(const T &device)
{
#if defined(_WIN32)
    wstring cmd = L"@" + device;
    std::vector<wchar_t> cmd_buf(cmd.begin(), cmd.end());
    cmd_buf.push_back('\0');
    PTSTR argv[] = {cmd_buf.data(), nullptr};
    cmdDisable(L"devcon", nullptr, 0, 1, argv);
#else
    usb_manager_.DisableDevice(device);
#endif
}
